tls:
  certificates:
    - certFile: /opt/certs/noahgao-net-crt/tls.crt
      keyFile: /opt/certs/noahgao-net-crt/tls.key

http:
    routers:
        dashboard:
            rule: Host(`traefik.{{ traefik_domain }}`)
            service: api@internal
        whoami:
            rule: Host(`whoami.{{ traefik_domain }}`)
            service: whoami
    services:
        whoami:
            loadBalancer:
                servers:
                    - url: http://whoami

tcp:
    routers:
        default:
            rule: HostSNI(`*`)
            service: default
    services:
        default:
            loadBalancer:
                servers:
                    - address: frps:443
