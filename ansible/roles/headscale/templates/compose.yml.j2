services:
  headscale:
    image: headscale/headscale:{{ headscale_version | default('0.23.0') }}
    container_name: headscale
    restart: unless-stopped
    ports:
      - "8080:8080"
      - "9090:9090"
      - "50443:50443"
    volumes:
      - /etc/headscale/config.yaml:/etc/headscale/config.yaml:ro
      - /var/lib/headscale:/var/lib/headscale
      - /var/run/headscale:/var/run/headscale
    command: headscale serve
    networks:
      - ingress
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.headscale.rule=Host(`{{ headscale_domain | default('headscale.' + ansible_fqdn) }}`)"
      - "traefik.http.routers.headscale.entrypoints=websecure"
      - "traefik.http.routers.headscale.tls=true"
      - "traefik.http.routers.headscale.tls.certresolver=letsencrypt"
      - "traefik.http.services.headscale.loadbalancer.server.port=8080"

networks:
  ingress:
    name: ingress
    external: true
